"""Test cases for user drafts API endpoints"""

import json
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth.models import User

from accounts.tests.factory import ProfileFactory
from base.tests.factory import UserFactory
from base.tests.base_setup import BaseTestSetup

# Import incident factories
from general_patient_visitor.tests.factory import GeneralPatientVisitorFactory
from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from patient_visitor_grievance.tests.factory import GrievanceFactory
from lost_and_found.tests.factory import LostAndFoundFactory
from medication_error.tests.factory import MedicationErrorFactory


class TestUserDraftsAPIEndpoints(BaseTestSetup):
    """Test cases for user drafts API endpoints"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.user = UserFactory()
        self.other_user = UserFactory()
        self.profile = ProfileFactory(user=self.user)
        self.other_profile = ProfileFactory(user=self.other_user)
        
        # URL for the drafts endpoint
        self.drafts_url = "/api/accounts/drafts/"

    def test_get_user_drafts_authenticated(self):
        """Test GET request for authenticated user"""
        # Create some draft incidents
        GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        AdverseDrugReactionFactory(created_by=self.user, status="Draft")
        
        # Create non-draft (should not be included)
        GeneralPatientVisitorFactory(created_by=self.user, status="Open")
        
        # Create draft for other user (should not be included)
        GeneralPatientVisitorFactory(created_by=self.other_user, status="Draft")

        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.drafts_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("general_incident", response.data)
        self.assertIn("adverse_drug_reaction", response.data)
        
        # Should have 1 draft in each category for this user
        self.assertEqual(len(response.data["general_incident"]), 1)
        self.assertEqual(len(response.data["adverse_drug_reaction"]), 1)

    def test_get_user_drafts_unauthenticated(self):
        """Test GET request without authentication"""
        response = self.client.get(self.drafts_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_get_user_drafts_with_user_id_param(self):
        """Test GET request with user_id parameter"""
        # Create drafts for both users
        GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        GeneralPatientVisitorFactory(created_by=self.other_user, status="Draft")

        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.drafts_url, {"user_id": self.other_user.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should get drafts for the other user
        self.assertEqual(len(response.data["general_incident"]), 1)

    def test_get_user_drafts_invalid_user_id(self):
        """Test GET request with invalid user_id parameter"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.drafts_url, {"user_id": "invalid"})

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Invalid user_id parameter", response.data["error"])

    def test_get_user_drafts_nonexistent_user_id(self):
        """Test GET request with non-existent user_id"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.drafts_url, {"user_id": 99999})

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("User not found", response.data["error"])

    def test_delete_user_drafts_success(self):
        """Test successful DELETE request"""
        # Create draft incidents
        draft1 = GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        draft2 = AdverseDrugReactionFactory(created_by=self.user, status="Draft")
        
        data = {
            "draft_ids": [
                {"id": draft1.id, "category": "general_incident"},
                {"id": draft2.id, "category": "adverse_drug_reaction"}
            ]
        }

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Successfully deleted", response.data["message"])
        self.assertEqual(response.data["data"]["deleted_count"], 2)

    def test_delete_user_drafts_unauthenticated(self):
        """Test DELETE request without authentication"""
        data = {"draft_ids": [{"id": 1, "category": "general_incident"}]}
        
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_delete_user_drafts_empty_body(self):
        """Test DELETE request with empty body"""
        self.client.force_authenticate(user=self.user)
        response = self.client.delete(self.drafts_url)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("Request body is required", response.data["error"])

    def test_delete_user_drafts_array_format(self):
        """Test DELETE request with simple array format"""
        draft1 = GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        
        # Test with array format (should be converted internally)
        data = [{"id": draft1.id, "category": "general_incident"}]

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Successfully deleted", response.data["message"])

    def test_delete_user_drafts_invalid_data(self):
        """Test DELETE request with invalid data"""
        data = {"draft_ids": []}

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("draft_ids must be a non-empty list", response.data["error"])

    def test_delete_user_drafts_invalid_category(self):
        """Test DELETE request with invalid category"""
        draft1 = GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        
        data = {
            "draft_ids": [
                {"id": draft1.id, "category": "invalid_category"}
            ]
        }

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        # Should return success but with errors
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("errors", response.data["data"])

    def test_delete_user_drafts_nonexistent_ids(self):
        """Test DELETE request with non-existent draft IDs"""
        data = {
            "draft_ids": [
                {"id": 99999, "category": "general_incident"}
            ]
        }

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        # Should handle gracefully with errors
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("errors", response.data["data"])

    def test_delete_user_drafts_malformed_items(self):
        """Test DELETE request with malformed draft items"""
        data = {
            "draft_ids": [
                {"id": 1},  # Missing category
                {"category": "general_incident"},  # Missing id
                "invalid_item"  # Not a dict
            ]
        }

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        # Should handle gracefully
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("errors", response.data["data"])

    def test_unsupported_http_methods(self):
        """Test unsupported HTTP methods"""
        self.client.force_authenticate(user=self.user)
        
        # Test POST
        response = self.client.post(self.drafts_url, {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        
        # Test PUT
        response = self.client.put(self.drafts_url, {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)
        
        # Test PATCH
        response = self.client.patch(self.drafts_url, {})
        self.assertEqual(response.status_code, status.HTTP_405_METHOD_NOT_ALLOWED)

    def test_get_user_drafts_empty_result(self):
        """Test GET request when user has no drafts"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.drafts_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # All categories should be present but empty
        expected_categories = [
            "general_incident", "adverse_drug_reaction", "grievance_incident",
            "grievance_investigation", "employee_incident", "health_investigation",
            "lost_and_found", "medical_error", "workplace_violence"
        ]
        
        for category in expected_categories:
            self.assertIn(category, response.data)
            self.assertEqual(len(response.data[category]), 0)

    def test_delete_user_drafts_multiple_categories(self):
        """Test DELETE request with multiple incident categories"""
        # Create drafts of different types
        draft1 = GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        draft2 = AdverseDrugReactionFactory(created_by=self.user, status="Draft")
        draft3 = GrievanceFactory(created_by=self.user, status="Draft")
        draft4 = LostAndFoundFactory(created_by=self.user, status="Draft")
        draft5 = MedicationErrorFactory(created_by=self.user, status="Draft")
        
        data = {
            "draft_ids": [
                {"id": draft1.id, "category": "general_incident"},
                {"id": draft2.id, "category": "adverse_drug_reaction"},
                {"id": draft3.id, "category": "grievance_incident"},
                {"id": draft4.id, "category": "lost_and_found"},
                {"id": draft5.id, "category": "medical_error"}
            ]
        }

        self.client.force_authenticate(user=self.user)
        response = self.client.delete(
            self.drafts_url,
            data=json.dumps(data),
            content_type="application/json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Successfully deleted", response.data["message"])
        self.assertEqual(response.data["data"]["deleted_count"], 5)
