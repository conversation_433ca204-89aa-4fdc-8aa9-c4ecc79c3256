"""Test cases for ProfileService draft management methods"""

from django.test import TestCase
from django.contrib.auth.models import User
from accounts.services.profile.services import ProfileService
from accounts.tests.factory import ProfileFactory
from base.tests.factory import UserFactory
from base.tests.base_setup import BaseTestSetup

# Import all the incident factories
from general_patient_visitor.tests.factory import GeneralPatientVisitorFactory
from adverse_drug_reaction.tests.factory import AdverseDrugReactionFactory
from patient_visitor_grievance.tests.factory import GrievanceFactory, GrievanceInvestigationFactory
from staff_incident_reports.tests.factory import StaffIncidentReportFactory, StaffIncidentInvestigationFactory
from lost_and_found.tests.factory import LostAndFoundFactory
from medication_error.tests.factory import MedicationErrorFactory
from workplace_violence_reports.tests.factory import WorkPlaceViolenceFactory


class TestProfileServiceDrafts(BaseTestSetup):
    """Test cases for ProfileService draft management functionality"""

    def setUp(self):
        """Set up test data"""
        super().setUp()
        self.user = UserFactory()
        self.other_user = UserFactory()
        self.profile = ProfileFactory(user=self.user)
        self.other_profile = ProfileFactory(user=self.other_user)
        self.profile_service = ProfileService(user=self.user)

    def test_get_user_drafts_success(self):
        """Test successful retrieval of user drafts"""
        GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        AdverseDrugReactionFactory(created_by=self.user, status="Draft")
        GrievanceFactory(created_by=self.user, status="Draft")
        
        GeneralPatientVisitorFactory(created_by=self.user, status="Open")
        
        GeneralPatientVisitorFactory(created_by=self.other_user, status="Draft")

        response = self.profile_service.get_user_drafts()
        print(response.message)

        self.assertTrue(response.success)
        self.assertIn("general_incident", response.data)
        self.assertIn("adverse_drug_reaction", response.data)
        self.assertIn("grievance_incident", response.data)
        
        # self.assertEqual(len(response.data["general_incident"]), 1)
        self.assertEqual(len(response.data["adverse_drug_reaction"]), 1)
        self.assertEqual(len(response.data["grievance_incident"]), 1)

    def test_get_user_drafts_for_specific_user(self):
        """Test retrieval of drafts for a specific user"""
        GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        GeneralPatientVisitorFactory(created_by=self.other_user, status="Draft")

        response = self.profile_service.get_user_drafts(user_id=self.other_user.id)

        self.assertTrue(response.success)
        self.assertEqual(len(response.data["general_incident"]), 1)

    def test_get_user_drafts_user_not_found(self):
        """Test error when specified user doesn't exist"""
        response = self.profile_service.get_user_drafts(user_id=99999)

        self.assertFalse(response.success)
        self.assertEqual(response.code, 404)

    def test_get_user_drafts_empty_result(self):
        """Test when user has no drafts"""
        response = self.profile_service.get_user_drafts()

        self.assertTrue(response.success)
        for category in response.data.values():
            self.assertEqual(len(category), 0)

    def test_delete_user_drafts_success(self):
        """Test successful deletion of user drafts"""
        draft1 = GeneralPatientVisitorFactory(created_by= self.super_user, status="Draft")
        draft2 = AdverseDrugReactionFactory(created_by= self.super_user, status="Draft")
        
        draft_data = {
            "draft_ids": [
                {"id": draft1.id, "category": "general_incident"},
                {"id": draft2.id, "category": "adverse_drug_reaction"}
            ]
        }

        response = self.profile_service.delete_user_drafts(draft_data)

        self.assertTrue(response.success)
        self.assertEqual(response.data["deleted_count"], 2)

    def test_delete_user_drafts_invalid_data(self):
        """Test deletion with invalid data format"""
        response = self.profile_service.delete_user_drafts({"draft_ids": []})
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

        response = self.profile_service.delete_user_drafts({"draft_ids": "invalid"})
        self.assertFalse(response.success)
        self.assertEqual(response.code, 400)

    def test_delete_user_drafts_invalid_category(self):
        """Test deletion with invalid category"""
        draft1 = GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        
        draft_data = {
            "draft_ids": [
                {"id": draft1.id, "category": "invalid_category"}
            ]
        }

        response = self.profile_service.delete_user_drafts(draft_data)

        self.assertTrue(response.success)
        self.assertIn("errors", response.data)
        self.assertEqual(response.data["deleted_count"], 0)

    def test_delete_user_drafts_nonexistent_ids(self):
        """Test deletion with non-existent draft IDs"""
        draft_data = {
            "draft_ids": [
                {"id": 99999, "category": "general_incident"}
            ]
        }

        response = self.profile_service.delete_user_drafts(draft_data)

        self.assertIn("errors", response.data if response.data else {})

    def test_delete_user_drafts_mixed_success_failure(self):
        """Test deletion with mix of valid and invalid items"""
        draft1 = GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        
        draft_data = {
            "draft_ids": [
                {"id": draft1.id, "category": "general_incident"},
                {"id": 99999, "category": "general_incident"},
                {"id": draft1.id, "category": "invalid_category"}
            ]
        }

        response = self.profile_service.delete_user_drafts(draft_data)

        self.assertTrue(response.success)
        self.assertIn("errors", response.data)
        self.assertGreater(response.data["deleted_count"], 0)

    def test_delete_user_drafts_malformed_items(self):
        """Test deletion with malformed draft items"""
        draft_data = {
            "draft_ids": [
                {"id": 1},
                {"category": "general_incident"},
                "invalid_item",
                {"id": "not_a_number", "category": "general_incident"}
            ]
        }

        response = self.profile_service.delete_user_drafts(draft_data)

        self.assertTrue(response.success)
        self.assertIn("errors", response.data)
        self.assertEqual(response.data["deleted_count"], 0)

    def test_get_user_drafts_all_incident_types(self):
        """Test that all incident types are included in draft retrieval"""
        GeneralPatientVisitorFactory(created_by=self.user, status="Draft")
        AdverseDrugReactionFactory(created_by=self.user, status="Draft")
        GrievanceFactory(created_by=self.user, status="Draft")
        GrievanceInvestigationFactory(created_by=self.user, status="Draft")
        StaffIncidentReportFactory(created_by=self.user, status="Draft")
        StaffIncidentInvestigationFactory(created_by=self.user, status="Draft")
        LostAndFoundFactory(created_by=self.user, status="Draft")
        MedicationErrorFactory(created_by=self.user, status="Draft")
        WorkPlaceViolenceFactory(created_by=self.user, status="Draft")

        response = self.profile_service.get_user_drafts()

        self.assertTrue(response.success)
        
        expected_categories = [
            "general_incident", "adverse_drug_reaction", "grievance_incident",
            "grievance_investigation", "employee_incident", "health_investigation",
            "lost_and_found", "medical_error", "workplace_violence"
        ]
        
        for category in expected_categories:
            self.assertIn(category, response.data)
            self.assertEqual(len(response.data[category]), 1)

    def test_delete_user_drafts_all_incident_types(self):
        """Test deletion of all incident types"""
        drafts = {
            "general_incident": GeneralPatientVisitorFactory(created_by=self.user, status="Draft"),
            "adverse_drug_reaction": AdverseDrugReactionFactory(created_by=self.user, status="Draft"),
            "grievance_incident": GrievanceFactory(created_by=self.user, status="Draft"),
            "grievance_investigation": GrievanceInvestigationFactory(created_by=self.user, status="Draft"),
            "employee_incident": StaffIncidentReportFactory(created_by=self.user, status="Draft"),
            "health_investigation": StaffIncidentInvestigationFactory(created_by=self.user, status="Draft"),
            "lost_and_found": LostAndFoundFactory(created_by=self.user, status="Draft"),
            "medical_error": MedicationErrorFactory(created_by=self.user, status="Draft"),
            "workplace_violence": WorkPlaceViolenceFactory(created_by=self.user, status="Draft"),
        }

        draft_data = {
            "draft_ids": [
                {"id": draft.id, "category": category}
                for category, draft in drafts.items()
            ]
        }

        response = self.profile_service.delete_user_drafts(draft_data)

        self.assertTrue(response.success)
        self.assertEqual(response.data["deleted_count"], 9)
